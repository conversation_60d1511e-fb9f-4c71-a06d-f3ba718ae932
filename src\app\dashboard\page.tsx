'use client'

import DashboardLayout from '@/components/DashboardLayout'

export default function Dashboard() {
  return (
    <DashboardLayout
      title="Dashboard"
      subtitle="Welcome to your sari-sari store admin dashboard"
    >
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard Test</h1>
        <p className="text-gray-600 mb-6">
          Testing if the FacebookHeader and YoutubeSidebar are working properly.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Test Cards */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Products</h3>
            <p className="text-3xl font-bold text-blue-600">10</p>
            <p className="text-sm text-gray-500">Total items</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Customers</h3>
            <p className="text-3xl font-bold text-green-600">25</p>
            <p className="text-sm text-gray-500">Registered</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Debts</h3>
            <p className="text-3xl font-bold text-orange-600">₱1,250</p>
            <p className="text-sm text-gray-500">Outstanding</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Revenue</h3>
            <p className="text-3xl font-bold text-purple-600">₱5,430</p>
            <p className="text-sm text-gray-500">This month</p>
          </div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">Layout Test</h2>
          <p className="text-blue-800">
            If you can see this message with the Facebook-style header at the top 
            and YouTube-style sidebar on the left, then the layout is working correctly.
          </p>
          <div className="mt-4 space-y-2">
            <p className="text-sm text-blue-700">
              ✓ Facebook-style header should be visible at the top
            </p>
            <p className="text-sm text-blue-700">
              ✓ YouTube-style sidebar should be visible on the left
            </p>
            <p className="text-sm text-blue-700">
              ✓ This content should be properly positioned with margins
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
