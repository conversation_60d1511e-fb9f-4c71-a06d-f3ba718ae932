'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { NotificationContainer } from './AnimatedNotification'
import { Bell, Search, User, Settings, Home, MessageCircle, Users, Store, Menu, ChevronDown } from 'lucide-react'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'

// Import the new YoutubeSidebar component
import YoutubeSidebar from './YoutubeSidebar'
// Import the new FacebookHeader component
import FacebookHeader from './FacebookHeader'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

// Page titles mapping
const pageInfo: Record<string, { title: string; subtitle: string }> = {
  '/dashboard': { title: 'Dashboard', subtitle: 'Overview & analytics' },
  '/products': { title: 'Products', subtitle: 'Manage your store inventory' },
  '/customers': { title: 'Customers', subtitle: 'Customer database' },
  '/debts': { title: 'Debts', subtitle: 'Track customer debts' },
  '/payments': { title: 'Payments', subtitle: 'Payment records' },
  '/reports': { title: 'Reports', subtitle: 'Business insights' },
}

export default function DashboardLayout({
  children,
  title,
  subtitle: _subtitle,
  actions: _actions
}: DashboardLayoutProps) {
  // Suppress unused variable warnings - parameters kept for interface consistency
  void _subtitle
  void _actions

  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const isHydrated = useHydrationSafe()

  // Get current page info
  const currentPageInfo = pageInfo[pathname] || { title: 'Dashboard', subtitle: 'Overview & analytics' }
  const pageTitle = title || currentPageInfo.title

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-dropdown]')) {
        setShowNotifications(false)
        setShowProfile(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Show loading state during hydration
  if (!isHydrated) {
    return (
      <div className="dashboard-layout min-h-screen bg-gray-50">
        {/* Loading Sidebar */}
        <aside className="fixed top-0 left-0 z-50 h-full w-64 bg-white border-r border-gray-200 -translate-x-full lg:translate-x-0">
          <div className="flex items-center h-14 px-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg"></div>
              <div>
                <div className="h-4 bg-gray-300 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
              </div>
            </div>
          </div>
          <div className="p-4 space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-3 p-2">
                <div className="w-5 h-5 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded flex-1"></div>
              </div>
            ))}
          </div>
        </aside>

        {/* Loading Header */}
        <header className="fixed top-0 right-0 left-64 h-14 bg-white border-b border-gray-200 z-30">
          <div className="flex items-center justify-between h-full px-4">
            <div className="h-6 bg-gray-300 rounded w-32"></div>
            <div className="flex space-x-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </header>

        {/* Loading Content */}
        <main className="ml-64 pt-14">
          <div className="p-6">
            <div className="h-8 bg-gray-300 rounded w-48 mb-4"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="dashboard-layout min-h-screen bg-gray-50">
      {/* YouTube-Style Sidebar */}
      <YoutubeSidebar
        isCollapsed={isCollapsed}
        onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />

      {/* Facebook-Style Header */}
      <FacebookHeader
        isCollapsed={isCollapsed}
        isMobileMenuOpen={isMobileMenuOpen}
        onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        showNotifications={showNotifications}
        showProfile={showProfile}
        onToggleNotifications={() => setShowNotifications(!showNotifications)}
        onToggleProfile={() => setShowProfile(!showProfile)}
      />

      {/* Main Content Area */}
      <main className={`transition-all duration-300 pt-16 ${
        isCollapsed ? 'ml-16' : 'ml-64'
      }`}>
        <div className="p-6">
          {children}
        </div>
      </main>

      {/* Notification Container */}
      <NotificationContainer />
    </div>
  )
}
