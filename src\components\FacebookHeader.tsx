'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Bell, 
  Search, 
  User, 
  Home, 
  MessageCircle, 
  Users, 
  Store, 
  Menu, 
  ChevronDown,
  Settings,
  LogOut,
  HelpCircle,
  Shield
} from 'lucide-react'

interface FacebookHeaderProps {
  isCollapsed: boolean
  isMobileMenuOpen: boolean
  onToggleCollapse: () => void
  onMobileMenuToggle: () => void
  showNotifications: boolean
  showProfile: boolean
  onToggleNotifications: () => void
  onToggleProfile: () => void
}

export default function FacebookHeader({
  isCollapsed,
  isMobileMenuOpen,
  onToggleCollapse,
  onMobileMenuToggle,
  showNotifications,
  showProfile,
  onToggleNotifications,
  onToggleProfile
}: FacebookHeaderProps) {
  const pathname = usePathname()

  return (
    <header className={`
      fixed top-0 right-0 h-16 facebook-header shadow-sm border-b border-gray-200 z-30
      transition-all duration-300 ease-in-out
      ${isCollapsed ? 'left-16' : 'left-64'}
    `}>
      <div className="flex items-center justify-between h-full px-4">
        {/* Left Section - Logo & Navigation */}
        <div className="flex items-center space-x-6">
          {/* Mobile Menu Button */}
          <button
            onClick={onMobileMenuToggle}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <Menu className="h-5 w-5 text-gray-700" />
          </button>

          {/* Desktop Hamburger Menu */}
          <button
            onClick={onToggleCollapse}
            className="hidden lg:flex p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <Menu className="h-5 w-5 text-gray-700" />
          </button>

          {/* Store Logo & Name */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-md">
              <Store className="h-5 w-5 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gray-900">Caparan Tindahan</h1>
              <p className="text-xs text-gray-500 -mt-1">Admin Dashboard</p>
            </div>
          </div>

          {/* Quick Navigation Tabs */}
          <nav className="hidden lg:flex items-center space-x-1">
            <Link
              href="/dashboard"
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                pathname === '/dashboard' 
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <Home className="h-4 w-4" />
              <span>Home</span>
            </Link>
            <Link
              href="/products"
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                pathname.startsWith('/products') 
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <Store className="h-4 w-4" />
              <span>Products</span>
            </Link>
            <Link
              href="/customers"
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                pathname.startsWith('/customers') 
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-600' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <Users className="h-4 w-4" />
              <span>Customers</span>
            </Link>
          </nav>
        </div>

        {/* Center Section - Facebook-Style Search */}
        <div className="flex-1 max-w-xl mx-8 hidden md:block">
          <div className="relative">
            <div className="flex items-center bg-gray-100 rounded-full overflow-hidden hover:bg-gray-200 focus-within:bg-white focus-within:facebook-search-focus transition-all duration-200">
              <div className="pl-4 pr-2">
                <Search className="h-4 w-4 text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Search Caparan Tindahan"
                className="flex-1 py-2.5 pr-4 bg-transparent text-sm text-gray-900 placeholder-gray-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        {/* Right Section - Facebook-Style Actions */}
        <div className="flex items-center space-x-2">
          {/* Mobile Search Button */}
          <button className="md:hidden p-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
            <Search className="h-5 w-5 text-gray-600" />
          </button>

          {/* Quick Actions */}
          <div className="hidden sm:flex items-center space-x-1">
            {/* Messages */}
            <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200">
              <MessageCircle className="h-5 w-5 text-gray-600" />
            </button>
            
            {/* Notifications */}
            <button
              className="relative p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
              onClick={onToggleNotifications}
              aria-label="View notifications"
            >
              <Bell className="h-5 w-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                3
              </span>
            </button>
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
              onClick={onToggleProfile}
              aria-label="Account menu"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-sm">
                <User className="h-5 w-5 text-white" />
              </div>
              <ChevronDown className="h-4 w-4 text-gray-500 hidden sm:block" />
            </button>
          </div>
        </div>
      </div>

      {/* Facebook-Style Notifications Dropdown */}
      {showNotifications && (
        <div className="absolute right-4 top-14 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 animate-fade-in">
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Notifications</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Mark all as read
              </button>
            </div>
          </div>
          <div className="max-h-96 overflow-y-auto">
            <div className="p-3 hover:bg-gray-50 cursor-pointer border-l-4 border-red-500">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <Bell className="h-6 w-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Low Stock Alert</p>
                  <p className="text-sm text-gray-600">3 products are running low on inventory</p>
                  <p className="text-xs text-blue-600 mt-1">2 minutes ago</p>
                </div>
              </div>
            </div>
            <div className="p-3 hover:bg-gray-50 cursor-pointer">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">New Customer Registration</p>
                  <p className="text-sm text-gray-600">Maria Santos has registered as a new customer</p>
                  <p className="text-xs text-gray-500 mt-1">1 hour ago</p>
                </div>
              </div>
            </div>
          </div>
          <div className="p-3 border-t border-gray-100">
            <button className="w-full text-center text-blue-600 hover:text-blue-700 text-sm font-medium">
              See all notifications
            </button>
          </div>
        </div>
      )}

      {/* Facebook-Style Profile Dropdown */}
      {showProfile && (
        <div className="absolute right-4 top-14 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 animate-fade-in">
          <div className="p-4 bg-gradient-to-br from-blue-50 to-white border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-md">
                <User className="h-8 w-8 text-white" />
              </div>
              <div>
                <p className="text-lg font-bold text-gray-900">Store Owner</p>
                <p className="text-sm text-gray-600">Administrator</p>
              </div>
            </div>
          </div>
          <div className="p-2">
            <Link
              href="/settings"
              className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <Settings className="h-5 w-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Settings & Privacy</span>
            </Link>
            <Link
              href="/help"
              className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <HelpCircle className="h-5 w-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Help & Support</span>
            </Link>
            <Link
              href="/security"
              className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <Shield className="h-5 w-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Security</span>
            </Link>
            <hr className="my-2" />
            <button className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 w-full text-left">
              <LogOut className="h-5 w-5 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Log Out</span>
            </button>
          </div>
        </div>
      )}
    </header>
  )
}
